<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="referrer" content="no-referrer">
    <title>上海市瀚林世家1室价格测评报告</title>
    <link rel="stylesheet" href="https://www.fs.dichanai.com/ai-cric/cdn/tailwindcss/2.2.19/dist/tailwind.min.css">
    <script src="https://www.fs.dichanai.com/ai-cric/cdn/echarts/5.4.3/dist/echarts.min.js"></script>
    <style>body {
        font-family: <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, "Droid Sans", "Helvetica Neue", "Droid Sans Fallback", "Heiti SC", "Hiragino Sans GB", "sans-self";
        color: #333;
        line-height: 1.6;
        background-color: #f5f7fa;
    }

    .banner {
        background: linear-gradient(135deg, #3182ce, #4299e1);
        padding: 2rem;
        color: white;
        border-radius: 0.5rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .section {
        background: white;
        border-radius: 0.5rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        margin-bottom: 1.5rem;
        overflow: hidden;
    }

    .section-header {
        padding: 1.25rem;
        border-bottom: 1px solid #edf2f7;
        background-color: #f8fafc;
    }

    .section-body {
        padding: 1.5rem;
    }

    .data-card {
        background: white;
        border-radius: 0.5rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        padding: 1.25rem;
        height: 100%;
    }

    .chart-container {
        width: 100%;
        min-width: 320px;
        height: 400px;
        margin: 1.5rem 0;
    }

    .map-container {
        width: 100%;
        min-width: 320px;
        height: 500px;
        margin: 1.5rem 0;
    }

    .positive {
        color: #e53e3e;
    }

    .negative {
        color: #38a169;
    }

    @media (max-width: 768px) {
        .chart-container {
            height: 300px;
        }

        .map-container {
            height: 400px;
        }
    }

    .image-grid {
        display: grid;
        grid-template-columns:repeat(3, 1fr);
        gap: 1rem;
        margin: 1.5rem 0;
    }

    .image-grid img {
        width: 100%;
        height: 200px;
        object-fit: cover;
        border-radius: 0.375rem;
    }

    @media (max-width: 768px) {
        .image-grid {
            grid-template-columns:repeat(2, 1fr);
        }
    }

    @media (max-width: 640px) {
        .image-grid {
            grid-template-columns:1fr;
        }
    }</style>
</head>
<body class="bg-gray-100">
<div class="container mx-auto px-4 py-8 max-w-6xl">
    <div class="banner mb-8"><h1 class="text-3xl font-bold mb-2">上海市瀚林世家1室价格测评报告</h1>
        <p class="text-lg opacity-90">生成日期：2025-06-27</p></div>
    <div class="section mb-8">
        <div class="section-body">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="data-card"><h3 class="text-lg font-semibold mb-2">评测时间</h3>
                    <p>2025年06月</p></div>
                <div class="data-card"><h3 class="text-lg font-semibold mb-2">数据来源</h3>
                    <p>中国房地产决策咨询系统（CRIC）、市场公开数据</p></div>
                <div class="data-card"><h3 class="text-lg font-semibold mb-2">免责申明</h3>
                    <p>评测是基于CRIC和市场公开数据，通过AI算法和模型运算得出结果，仅供参考</p></div>
            </div>
        </div>
    </div>
    <div class="section mb-8">
        <div class="section-header"><h2 class="text-xl font-bold">一、基本情况</h2></div>
        <div class="section-body"><h3 class="text-lg font-semibold mb-4">1、小区信息</h3>
            <div class="overflow-x-auto">
                <table class="min-w-full bg-white border border-gray-200">
                    <tbody>
                    <tr class="border-b border-gray-200">
                        <td class="px-4 py-3 text-gray-600 font-medium bg-gray-50 w-1/4">地址</td>
                        <td class="px-4 py-3 w-1/4">武定路1128弄(武定路地铁站1号口步行400米)</td>
                        <td class="px-4 py-3 text-gray-600 font-medium bg-gray-50 w-1/4">所属城市</td>
                        <td class="px-4 py-3 w-1/4">上海市</td>
                    </tr>
                    <tr class="border-b border-gray-200">
                        <td class="px-4 py-3 text-gray-600 font-medium bg-gray-50">所属行政区</td>
                        <td class="px-4 py-3">静安区</td>
                        <td class="px-4 py-3 text-gray-600 font-medium bg-gray-50">所属板块</td>
                        <td class="px-4 py-3">曹家渡</td>
                    </tr>
                    <tr class="border-b border-gray-200">
                        <td class="px-4 py-3 text-gray-600 font-medium bg-gray-50">建筑年代</td>
                        <td class="px-4 py-3">2005-2006</td>
                        <td class="px-4 py-3 text-gray-600 font-medium bg-gray-50">物业类型</td>
                        <td class="px-4 py-3">住宅</td>
                    </tr>
                    <tr class="border-b border-gray-200">
                        <td class="px-4 py-3 text-gray-600 font-medium bg-gray-50">总户数</td>
                        <td class="px-4 py-3">735</td>
                        <td class="px-4 py-3 text-gray-600 font-medium bg-gray-50"></td>
                        <td class="px-4 py-3"></td>
                    </tr>
                    <tr class="border-b border-gray-200">
                        <td class="px-4 py-3 text-gray-600 font-medium bg-gray-50">绿化率</td>
                        <td class="px-4 py-3">35%</td>
                        <td class="px-4 py-3 text-gray-600 font-medium bg-gray-50">容积率</td>
                        <td class="px-4 py-3">400%</td>
                    </tr>
                    <tr class="border-b border-gray-200">
                        <td class="px-4 py-3 text-gray-600 font-medium bg-gray-50">建筑类型</td>
                        <td class="px-4 py-3">板楼</td>
                        <td class="px-4 py-3 text-gray-600 font-medium bg-gray-50">停车位</td>
                        <td class="px-4 py-3">550</td>
                    </tr>
                    <tr class="border-b border-gray-200">
                        <td class="px-4 py-3 text-gray-600 font-medium bg-gray-50">开发商</td>
                        <td class="px-4 py-3">上海静安置业(集团)有限公司</td>
                        <td class="px-4 py-3 text-gray-600 font-medium bg-gray-50">物业公司</td>
                        <td class="px-4 py-3">巨星物业</td>
                    </tr>
                    <tr>
                        <td class="px-4 py-3 text-gray-600 font-medium bg-gray-50">物业费</td>
                        <td class="px-4 py-3">1.65-2.6元/月/㎡</td>
                        <td class="px-4 py-3 text-gray-600 font-medium bg-gray-50"></td>
                        <td class="px-4 py-3"></td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <div class="section mb-8">
        <div class="section-header"><h2 class="text-xl font-bold">二、测评结论</h2></div>
        <div class="section-body"><h3 class="text-lg font-semibold mb-4">1、测评价格</h3>
            <div class="bg-blue-50 p-4 rounded-lg mb-6"><p class="text-xl">户型：<span class="font-bold">1室</span></p>
                <p class="text-xl">评测价格：<span class="font-bold text-blue-700">106,876元/㎡</span></p>
                <p class="text-sm text-gray-600 mt-2">（备注：以上评测价格是基于CRIC中国房地产决策咨询系统和市场公开数据，通过AI算法和模型运算得出结果，仅供参考。）</p></div>
            <h3 class="text-lg font-semibold mb-4">2、整体评价</h3>
            <div class="overflow-x-auto">
                <table class="min-w-full bg-white border border-gray-200">
                    <thead>
                    <tr class="bg-gray-100">
                        <th class="px-4 py-3 border-b text-left">序号</th>
                        <th class="px-4 py-3 border-b text-left">指标</th>
                        <th class="px-4 py-3 border-b text-left">评价</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr class="border-b">
                        <td class="px-4 py-3 align-top">1</td>
                        <td class="px-4 py-3 font-medium align-top">区域价值</td>
                        <td class="px-4 py-3"> 坐落于静安区武定路1128弄，处静安寺与曹家渡板块交界核心地带，均价约117234元/㎡，属区域高端定位。 <br><br> 建于2004年，成交价格稳定，拥有多样化户型，周边配套成熟，环境幽静，绿化率高。 <br><br> 与同区竞品相比，其稀缺性、区位优势及静安寺商圈支持使其保值性强，对高净值人群极具吸引力。
                        </td>
                    </tr>
                    <tr class="border-b">
                        <td class="px-4 py-3 align-top">2</td>
                        <td class="px-4 py-3 font-medium align-top">交通网络</td>
                        <td class="px-4 py-3"> 交通便捷度优于静安区96%的小区，2公里范围内拥有8个地铁站和137个公交站点，形成全方位覆盖网络。 <br><br> 距武定路地铁站仅303米，步行可达，公交系统发达，路网布局合理，为居民提供多元出行选择。 <br><br> 优越的交通条件不仅提升日常生活便利性，也为物业保值增值提供强大支撑。
                        </td>
                    </tr>
                    <tr class="border-b">
                        <td class="px-4 py-3 align-top">3</td>
                        <td class="px-4 py-3 font-medium align-top">生活配套</td>
                        <td class="px-4 py-3"> 成熟社区配套极为齐全，3公里内拥有91个购物中心、1112家超市，购物便利性超越本区62%小区； <br><br> 医疗资源方面，43家医院分布周边，优于本区88%同类小区； <br><br> 交通出行便捷，教育资源丰富且优质，包括常熟幼儿园、一师附小等名校，构成全龄教育网络，综合配套质量在静安区内表现出色。
                        </td>
                    </tr>
                    <tr class="border-b">
                        <td class="px-4 py-3 align-top">4</td>
                        <td class="px-4 py-3 font-medium align-top">教育资源</td>
                        <td class="px-4 py-3"> 教育配套评分超越静安区92%的小区，3公里范围内拥有116所幼儿园、52所小学及52所中学，构建完整教育体系。 <br><br> 包括上海市一师附小、市西初中分部、上海戏剧学院附中等名校，教育资源不仅数量充足，质量也相当优越。 <br><br> 对重视子女教育的家庭而言，瀚林世家提供了极具竞争力的学区价值与全龄段教育支持。
                        </td>
                    </tr>
                    <tr>
                        <td class="px-4 py-3 align-top">5</td>
                        <td class="px-4 py-3 font-medium align-top">小区品质</td>
                        <td class="px-4 py-3"> 2004年建成，14栋楼共668户，建筑面积91383㎡，呈板楼结构。40%的绿化率营造宜人环境，3.5的容积率保持适度密度。 <br><br> 主打带独立衣帽间、明卫、明厨南向客厅的实用户型，由巨星物业管理。 <br><br> 周边医疗、购物、教育配套齐全，地铁站近在咫尺，整体居住品质优良，均价10.8万/㎡反映市场对其高度认可。
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <div class="section mb-8">
        <div class="section-header"><h2 class="text-xl font-bold">三、评测分析</h2></div>
        <div class="section-body"><h3 class="text-lg font-semibold mb-4">周边竞品</h3>
            <p class="mb-4">周边0.5公里有二手房项目139个，均价8.5万元/㎡，瀚林世家均价109054元/平方米，在周边整体房价中处于较高水平。</p>
            <div id="map-container" class="map-container"></div>
            <div class="chart-container" id="price-chart"></div>
            <div class="bg-gray-50 p-5 rounded-lg mb-6"><h3 class="text-xl font-bold mb-4">瀚林世家小区评价分析</h3>
                <p class="mb-3">截至2025年6月，瀚林世家的二手房均价约为104,000-116,000元/平方米，近一年半的成交单价区间在100,000-116,596元/平方米。</p> <h4 class="text-lg font-semibold mt-5 mb-3">成交价格分析</h4>
                <p class="mb-3">瀚林世家近期具体成交案例：</p>
                <ul class="list-disc pl-5 mb-4">
                    <li class="mb-1">2025年3月，成交单价约104,902元/平方米</li>
                    <li class="mb-1">2025年2月，成交单价约116,596元/平方米</li>
                    <li class="mb-1">2024年12月，成交单价约106,767元/平方米</li>
                    <li class="mb-1">2024年7月，成交单价约100,000元/平方米</li>
                    <li class="mb-1">2024年4月，成交单价约108,947元/平方米</li>
                </ul>
                <h4 class="text-lg font-semibold mt-5 mb-3">与周边竞品对比</h4>
                <p class="mb-3">瀚林世家与周边竞品相比具有明显的价格优势：</p>
                <ol class="list-decimal pl-5 mb-4">
                    <li class="mb-1">与万航渡路433弄相比：2025年1月该小区成交均价为73,958元/平方米，瀚林世家同期价格高出约42,000元/平方米</li>
                    <li class="mb-1">与欣宏嘉园相比：2025年5月该小区成交均价为78,272元/平方米，而瀚林世家近期均价在105,000元/平方米左右，高出约27,000元/平方米</li>
                </ol>
                <h4 class="text-lg font-semibold mt-5 mb-3">价格走势</h4>
                <p class="mb-3">瀚林世家的房价从2023年7月至今整体保持稳定，略有波动：</p>
                <ul class="list-disc pl-5 mb-4">
                    <li class="mb-1">2023年7月达到峰值116,889元/平方米</li>
                    <li class="mb-1">2024年7月出现近期最低点100,000元/平方米</li>
                    <li class="mb-1">2025年初再次回升，2月份达到116,596元/平方米</li>
                </ul>
                <p>价格差异主要受户型大小、楼层、朝向和装修状况等因素影响。瀚林世家作为区域内高端住宅，其价值持续获得市场认可，相比周边竞品具有明显溢价。</p></div>
            <div class="mb-8"><h3 class="text-xl font-semibold mb-4">区域价值</h3>
                <p class="mb-4"> 瀚林世家位于上海市静安区武定路1128弄，地处市中心静安寺和曹家渡板块交界的核心地带，是静安区知名的高端住宅小区之一。小区建于2004年，总户数668户，主推1-3室、建筑面积约57-134㎡的多样化户型，由上海巨星物业有限公司负责管理。 </p>
                <p class="mb-4"> 从区域价值角度分析，该小区2025年6月最新均价约117234元/㎡，在静安区处于较高水平，且历史成交价格稳定，今年初部分成交如93㎡户型总价1096万，132㎡户型总价1420万，充分体现了优质地段的抗跌性。 </p>
                <p class="mb-4"> 对比同区四和花园111369元/㎡、达安花园98868元/㎡等小区，瀚林世家的市场表现属于高端定位。区域配套方面，小区周边设施成熟，步行可达多家银行、优质教育资源如常熟幼儿园等，环境安静、绿化率较高。 </p>
                <p>
                    交通虽主要依赖地铁和自驾，但区域地段优越使得通勤及获取城市核心资源十分便利。作为静安区内环核心区域的代表性小区，瀚林世家受益于静安寺商圈、江宁路和曹家渡等商业生活圈支撑，加上老牌优质小区的稀缺性，其区域价值持续稳固并有望稳中有升，对高净值人群具有显著吸引力，无论自住还是投资都具备极高的区域价值。 </p>
                <div class="flex justify-center my-6"><img src="https://www.fs.dichanai.com/ai-cric/report/diagrams/concepts/2025/06/27/0920957b179cf9e067d2f2618dc664a2.svg" alt="区域价值配图" class="max-w-full h-auto" referrerPolicy="no-referrer"></div>
            </div>
            <div class="mb-8"><h3 class="text-xl font-semibold mb-4">交通网络</h3>
                <p class="mb-4"> 瀚林世家小区位于上海市静安区，拥有极为出色的交通网络配套，其交通便捷性在区域内表现突出，根据数据显示，小区交通配套得分优于静安区96%的小区，为居民日常出行提供了极大便利。 </p>
                <p class="mb-4"> 在轨道交通方面，小区周边2公里直线范围内分布有8个地铁站，包括武定路站、昌平路站等重要站点，居民可通过步行或短距离接驳方式快速乘坐地铁前往市区各主要区域。 </p>
                <p class="mb-4"> 公共交通同样配套完善，2公里范围内分布有137个公交站点，其中武定路武宁南路站、武定路万春街站等主要站点线路覆盖密集，能够满足居民多样化的出行需求，为通勤、购物及休闲活动提供了灵活选择。 </p>
                <p> 瀚林世家的交通优势不仅为居民日常生活带来便利，也为小区物业的保值增值提供了有力支撑，使居民出门即可享受上海城市高效联动的交通网络，是静安区内交通配套特别完善的居住社区。 </p>
                <div class="flex justify-center my-6"><img src="https://www.fs.dichanai.com/ai-cric/report/diagrams/map/2025/06/27/b55bb307474701345e455af1af0164c4.svg" alt="交通网络配图" class="max-w-full h-auto" referrerPolicy="no-referrer"></div>
            </div>
            <div class="mb-8"><h3 class="text-xl font-semibold mb-4">生活配套</h3>
                <p class="mb-4"> 瀚林世家小区位于静安区武定路1128弄，作为2004年竣工的成熟住宅社区，其生活配套设施可谓十分丰富。小区周边3公里范围内拥有91个购物中心（包括PAC购物中心、CP静安）和1112家超市（如康武食品、J'sgrocery），使得日常采买极为便利，购物配套评分优于静安区62%的小区。 </p>
                <p class="mb-4"> 医疗资源方面，尽管三公里范围内缺少三甲医院，但总计有43家医院分布在周边，整体医疗配套表现优于本区88%的小区，基本满足居民就医需求。 </p>
                <p class="mb-4"> 交通方面表现突出，距离武定路地铁站仅303米，步行即可到达，周边还有武定路武宁南路公交站，93路等多条公交线路经过，出行极为便捷。 </p>
                <p>
                    值得一提的是，小区周边教育资源丰富，3公里范围内分布有116个幼儿园（如常熟幼儿园、常德书法幼儿园），52所小学（包括一师附小等知名学校），以及上海戏剧学院附属高级中学等优质中学资源，能够满足不同阶段的教育需求。综合来看，瀚林世家凭借其完善的生活配套、便捷的交通条件和丰富的教育资源，为居民提供了高品质的居住环境，堪称静安区生活配套较为完备的住宅小区之一。 </p>
                <div class="flex justify-center my-6"><img src="https://www.fs.dichanai.com/ai-cric/report/diagrams/concepts/2025/06/27/4487e050fa79b2e382a554bd739e7ec8.svg" alt="生活配套配图" class="max-w-full h-auto" referrerPolicy="no-referrer"></div>
            </div>
            <div class="mb-8"><h3 class="text-xl font-semibold mb-4">教育资源</h3>
                <p class="mb-4"> 瀚林世家小区位于上海市静安区，拥有极其丰富的教育资源配套，在静安区的住宅小区中占据教育优势地位。据统计，小区周边3公里范围内有高达116所幼儿园资源，包括上海市静安区常熟幼儿园（延平路）、上海市静安区常德书法幼儿园四和部等优质幼教机构。 </p>
                <p class="mb-4"> 在义务教育阶段，瀚林世家周边拥有52所小学和52所中学，覆盖了从基础教育到高中阶段的完整学段，其中不乏上海市一师附小、上海戏剧学院附属高级中学、上海市市西初级中学（分部）等知名学府。 </p>
                <p class="mb-4"> 从数据来看，瀚林世家在教育配套得分方面高于本区域92%的小区，堪称静安区内教育资源配置非常优越的小区之一。对于重视子女教育的家庭而言，瀚林世家无疑是一个理想的居住选择。 </p>
                <p> 当然，实际入学政策仍需参考当年静安区教育局的最新规定和学区划分，建议有意向的家庭进一步咨询专业人士获取权威信息。总体而言，瀚林世家周边教育资源不仅在数量上充足，在质量上也相当优越，为住户提供了全方位的教育配套保障。 </p>
                <div class="flex justify-center my-6"><img src="https://www.fs.dichanai.com/ai-cric/report/diagrams/concepts/2025/06/27/d3394efb69257204bad9d093341868c9.svg" alt="教育资源配图" class="max-w-full h-auto" referrerPolicy="no-referrer"></div>
            </div>
            <div class="mb-8"><h3 class="text-xl font-semibold mb-4">小区品质</h3>
                <p class="mb-4"> 瀚林世家位于上海市静安区武定路1128弄，由上海静安置业（集团）有限公司开发，于2004年建成并于2005-2006年交付，是静安区内一处品质较佳的居住社区。小区规模适中，共有14栋楼，总户数668户，建筑面积约91383㎡，采用板楼结构设计，整体布局宽松合理。 </p>
                <p class="mb-4"> 值得关注的是，该小区绿化率高达40%，为居民提供了良好的自然环境，同时容积率为3.5，在城区住宅中保持了适度的建筑密度。户型设计方面，小区主打带独立衣帽间、明卫、明厨、南向客厅的两居户型，满足不同家庭的居住需求。小区由上海巨星物业有限公司负责管理，服务评价尚可。 </p>
                <p class="mb-4"> 周边配套极为丰富，医疗资源在本区域内优于88%的小区，3公里范围内有43个医院；购物便利性高于本区域62%的小区，拥有91个购物中心、1112个超市；教育资源丰富，附近有优质幼儿园、小学和中学；交通方面距离地铁武定路站仅303米，公交线路众多。 </p>
                <p> 整体而言，瀚林世家凭借其成熟的社区环境、丰富的周边配套和良好的交通条件，成为静安区内居住品质较高、保值性良好的住宅小区，2025年均价约10.8万元/㎡，市场认可度高。 </p>
                <div class="flex justify-center my-6"><img src="https://www.fs.dichanai.com/ai-cric/report/diagrams/concepts/2025/06/27/2e1bd15094c86eea782052e6892235e7.svg" alt="小区品质配图" class="max-w-full h-auto" referrerPolicy="no-referrer"></div>
            </div>
        </div>
    </div>
</div>
<script>window.addEventListener('beforeprint', convertChartsToImages);

function convertChartsToImages() {
    const chartContainers = document.querySelectorAll('.chart-container');
    chartContainers.forEach(container => {
        try {
            const chart = echarts.getInstanceByDom(container);
            if (chart && !chart.isDisposed()) {
                const dataURL = chart.getDataURL({type: 'png', pixelRatio: 2, backgroundColor: '#fff'});
                container.setAttribute('data-original-html', container.innerHTML);
                container.innerHTML = `<img src="${dataURL}" style="width:100%;height:auto;display:block;" alt="Chart">`;
            }
        } catch (error) {
            console.error('转换图表为图片时出错:', error);
        }
    });
};document.addEventListener('DOMContentLoaded', function () {
    try {
        const priceChartDom = document.getElementById('price-chart');
        if (priceChartDom) {
            const priceChart = echarts.init(priceChartDom);
            const months = ['2023年06月', '2023年07月', '2023年08月', '2023年09月', '2023年10月', '2023年11月', '2023年12月', '2024年01月', '2024年02月', '2024年03月', '2024年04月', '2024年05月', '2024年06月', '2024年07月', '2024年08月', '2024年09月', '2024年10月', '2024年11月', '2024年12月', '2025年01月', '2025年02月', '2025年03月', '2025年04月', '2025年05月'];
            const hanlinsData = [null, 116889, null, null, null, 104653, null, null, null, null, 108947, null, null, 100000, null, null, null, null, 106767, null, 116596, 104902, null, null];
            const whdData = [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 73958, null, null, null, null];
            const xhjyData = [null, null, null, null, null, null, null, null, null, null, null, null, null, 82826, null, null, null, null, null, null, null, null, null, 78272];
            const option = {
                title: {text: '瀚林世家周边成交价格走势(元/㎡)', left: 'center', top: 10},
                tooltip: {
                    trigger: 'axis', formatter: function (params) {
                        let result = params[0].name + '<br/>';
                        params.forEach(param => {
                            if (param.value !== null) {
                                let marker = '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:' + param.color + '"></span>';
                                result += marker + param.seriesName + ': ' + param.value + '元/㎡<br/>';
                            }
                        });
                        return result;
                    }
                },
                legend: {data: ['瀚林世家', '万航渡路433弄', '欣宏嘉园'], bottom: 10},
                grid: {containLabel: true, left: '3%', right: '4%', bottom: '15%'},
                xAxis: {type: 'category', data: months, axisLabel: {interval: 2, rotate: 45}},
                yAxis: {type: 'value', name: '价格(元/㎡)', min: 60000, axisLabel: {formatter: '{value}'}},
                series: [{name: '瀚林世家', type: 'line', data: hanlinsData, symbolSize: 8, lineStyle: {width: 3}, itemStyle: {color: '#3182CE'}}, {name: '万航渡路433弄', type: 'line', data: whdData, symbolSize: 8, lineStyle: {width: 3}, itemStyle: {color: '#E53E3E'}}, {
                    name: '欣宏嘉园',
                    type: 'line',
                    data: xhjyData,
                    symbolSize: 8,
                    lineStyle: {width: 3},
                    itemStyle: {color: '#38A169'}
                }],
                media: [{query: {maxWidth: 768}, option: {title: {show: false}, legend: {show: false}, grid: {top: 10, right: 10, left: 40, bottom: 20, containLabel: true}}}]
            };
            priceChart.setOption(option);
            window.addEventListener('resize', function () {
                priceChart.resize();
            });
        }
    } catch (error) {
        console.error('价格走势图初始化失败:', error);
        if (document.getElementById('price-chart')) {
            document.getElementById('price-chart').innerHTML = '<div class="flex flex-col items-center justify-center h-full"><p class="text-red-500 mb-2">图表加载失败,请联系客服</p><img src="https://www.dichanai.com/images/equity/code.png" alt="客服二维码" class="w-32"></div>';
        }
    }
    try {
        const script = document.createElement('script');
        script.src = 'https://webapi.amap.com/maps?v=2.0&key=d3ae5b12406f08cc497ecbb564bb7a15&callback=initMap';
        document.head.appendChild(script);
        window.initMap = function () {
            const map = new AMap.Map('map-container', {zoom: 15, center: [121.445138, 31.235518], viewMode: '2D', mapStyle: 'amap://styles/whitesmoke'});
            const mainMarker = new AMap.Marker({
                position: [121.445138, 31.235518],
                title: '瀚林世家(本小区)',
                icon: new AMap.Icon({
                    size: new AMap.Size(16, 16),
                    image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48Y2lyY2xlIGN4PSI4IiBjeT0iOCIgcj0iNyIgZmlsbD0iIzMxODJDRSIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIyIi8+PC9zdmc+',
                    imageSize: new AMap.Size(16, 16)
                })
            });
            mainMarker.setMap(map);
            const circle = new AMap.Circle({center: [121.445138, 31.235518], radius: 500, strokeColor: "#3182CE", strokeOpacity: 0.8, strokeWeight: 2, fillColor: "#3182CE", fillOpacity: 0.1});
            circle.setMap(map);
            const neighborhoodData = [{name: '康定路935弄', lng: 121.4455117, lat: 31.23749972}, {name: '万航渡路249弄', lng: 121.4461602, lat: 31.23208599}, {name: '联业公寓', lng: 121.440395, lat: 31.234798}, {name: '九龙仓静安壹号', lng: 121.44387083906, lat: 31.2312428595}, {
                name: '胶州路398弄',
                lng: 121.4487679,
                lat: 31.23704652
            }, {name: '康定路863弄', lng: 121.4475248102, lat: 31.237265696002}, {name: '胶州路220弄', lng: 121.450096, lat: 31.234299}, {name: '延平路425号', lng: 121.44396612639, lat: 31.239836062116}, {name: '武定南路111号', lng: 121.442358, lat: 31.233799}, {
                name: '中汇商务中心',
                lng: 121.44516585023,
                lat: 31.237428364697
            }, {name: '武宁南路88弄1号', lng: 121.4436331461, lat: 31.233059951017}, {name: '万航渡路229弄', lng: 121.4472711, lat: 31.23213591}, {name: '胶州路225弄', lng: 121.4498017037, lat: 31.23418249341}, {name: '沪中新苑', lng: 121.444086, lat: 31.233545}, {
                name: '延平路266弄',
                lng: 121.445977,
                lat: 31.237326
            }, {name: '天合大厦', lng: 121.44483894932, lat: 31.237727026983}, {name: '金家巷小区', lng: 121.446602, lat: 31.232633}, {name: '鸿力公寓', lng: 121.44393, lat: 31.235459}, {name: '万航渡路433弄', lng: 121.4428567, lat: 31.23158361}, {name: '欣宏嘉园', lng: 121.440347, lat: 31.235072}];
            neighborhoodData.forEach(item => {
                const marker = new AMap.Marker({
                    position: [item.lng, item.lat],
                    title: item.name,
                    icon: new AMap.Icon({
                        size: new AMap.Size(10, 10),
                        image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAiIGhlaWdodD0iMTAiIHZpZXdCb3g9IjAgMCAxMCAxMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48Y2lyY2xlIGN4PSI1IiBjeT0iNSIgcj0iNSIgZmlsbD0iIzMxODJDRSIvPjwvc3ZnPg==',
                        imageSize: new AMap.Size(10, 10)
                    })
                });
                marker.setMap(map);
                AMap.event.addListener(marker, 'click', function () {
                    new AMap.InfoWindow({content: `<div style="padding: 8px; font-size: 12px;">${item.name}</div>`, offset: new AMap.Pixel(0, -10)}).open(map, marker.getPosition());
                });
            });
        };
    } catch (error) {
        console.error('地图加载失败:', error);
        if (document.getElementById('map-container')) {
            document.getElementById('map-container').innerHTML = '<div class="flex flex-col items-center justify-center h-full"><p class="text-red-500 mb-2">地图加载失败,请联系客服</p><img src="https://www.dichanai.com/images/equity/code.png" alt="客服二维码" class="w-32"></div>';
        }
    }
});</script>
</body>
</html>