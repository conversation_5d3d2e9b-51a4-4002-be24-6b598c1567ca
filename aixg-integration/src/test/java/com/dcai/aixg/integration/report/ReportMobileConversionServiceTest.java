package com.dcai.aixg.integration.report;

import com.dcai.aixg.integration.report.model.MobileConversionResult;
import com.dcai.aixg.integration.report.model.ReportData;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * 报告手机端转换服务测试
 */
class ReportMobileConversionServiceTest {
    
    @Mock
    private MobileStyleConverter mobileStyleConverter;
    
    private MobileConversionConfig conversionConfig;
    private ReportMobileConversionService conversionService;
    
    @TempDir
    Path tempDir;
    
    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        
        // 创建配置
        conversionConfig = new MobileConversionConfig();
        conversionConfig.setEnabled(true);
        conversionConfig.setMaxFileSize(10 * 1024 * 1024); // 10MB
        conversionConfig.setTimeoutMs(30000); // 30秒
        conversionConfig.setIncludeOriginalData(true);
        
        conversionService = new ReportMobileConversionService(mobileStyleConverter, conversionConfig);
    }
    
    @Test
    void testConvertToMobileSuccess() throws IOException {
        // 准备测试数据
        String testHtml = "<html><body><h1>测试报告</h1></body></html>";
        when(mobileStyleConverter.convertToMobileHtml(anyString())).thenReturn(testHtml);
        
        // 创建测试文件
        Path testFile = tempDir.resolve("test-report.html");
        Files.write(testFile, "test content".getBytes());
        
        // 执行转换
        MobileConversionResult result = conversionService.convertToMobile(testFile.toString());
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(testHtml, result.getMobileHtml());
        assertNotNull(result.getConversionStats());
        assertTrue(result.getConversionTimestamp() > 0);
        assertNull(result.getErrorMessage());
    }
    
    @Test
    void testConvertToMobileFileNotExists() {
        // 测试文件不存在的情况
        String nonExistentFile = tempDir.resolve("non-existent.html").toString();
        
        MobileConversionResult result = conversionService.convertToMobile(nonExistentFile);
        
        // 验证结果
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertNotNull(result.getErrorMessage());
        assertTrue(result.getErrorMessage().contains("文件不存在"));
        assertNull(result.getMobileHtml());
    }
    
    @Test
    void testConvertToMobileFileSizeExceeded() throws IOException {
        // 设置较小的文件大小限制
        conversionConfig.setMaxFileSize(10); // 10 bytes
        
        // 创建超过大小限制的测试文件
        Path testFile = tempDir.resolve("large-file.html");
        Files.write(testFile, "This is a test content that exceeds the size limit".getBytes());
        
        MobileConversionResult result = conversionService.convertToMobile(testFile.toString());
        
        // 验证结果
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertNotNull(result.getErrorMessage());
        assertTrue(result.getErrorMessage().contains("文件大小超过限制"));
    }
    
    @Test
    void testConvertToMobileDisabled() throws IOException {
        // 禁用转换功能
        conversionConfig.setEnabled(false);
        
        // 创建测试文件
        Path testFile = tempDir.resolve("test-report.html");
        Files.write(testFile, "test content".getBytes());
        
        MobileConversionResult result = conversionService.convertToMobile(testFile.toString());
        
        // 验证结果
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertNotNull(result.getErrorMessage());
        assertTrue(result.getErrorMessage().contains("手机端转换功能未启用"));
    }
    
    @Test
    void testConvertToMobileException() throws IOException {
        // 模拟转换异常
        when(mobileStyleConverter.convertToMobileHtml(anyString()))
                .thenThrow(new RuntimeException("转换异常"));
        
        // 创建测试文件
        Path testFile = tempDir.resolve("test-report.html");
        Files.write(testFile, "test content".getBytes());
        
        MobileConversionResult result = conversionService.convertToMobile(testFile.toString());
        
        // 验证结果
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertNotNull(result.getErrorMessage());
        assertTrue(result.getErrorMessage().contains("转换失败"));
    }
    
    @Test
    void testConvertToMobileAsync() throws IOException, ExecutionException, InterruptedException {
        // 准备测试数据
        String testHtml = "<html><body><h1>异步测试报告</h1></body></html>";
        when(mobileStyleConverter.convertToMobileHtml(anyString())).thenReturn(testHtml);
        
        // 创建测试文件
        Path testFile = tempDir.resolve("async-test-report.html");
        Files.write(testFile, "test content".getBytes());
        
        // 执行异步转换
        CompletableFuture<MobileConversionResult> future = 
                conversionService.convertToMobileAsync(testFile.toString());
        
        // 等待结果
        MobileConversionResult result = future.get();
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(testHtml, result.getMobileHtml());
    }
    
    @Test
    void testConvertAndSave() throws IOException {
        // 准备测试数据
        String testHtml = "<html><body><h1>保存测试报告</h1></body></html>";
        when(mobileStyleConverter.convertToMobileHtml(anyString())).thenReturn(testHtml);
        
        // 创建测试文件
        Path testFile = tempDir.resolve("save-test-report.html");
        Files.write(testFile, "test content".getBytes());
        
        // 执行转换并保存
        Path outputFile = tempDir.resolve("output-mobile.html");
        MobileConversionResult result = conversionService.convertAndSave(
                testFile.toString(), outputFile.toString());
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        
        // 验证文件已保存
        assertTrue(Files.exists(outputFile));
        String savedContent = Files.readString(outputFile);
        assertEquals(testHtml, savedContent);
    }
    
    @Test
    void testBatchConvert() throws IOException, ExecutionException, InterruptedException {
        // 准备测试数据
        String testHtml1 = "<html><body><h1>批量测试报告1</h1></body></html>";
        String testHtml2 = "<html><body><h1>批量测试报告2</h1></body></html>";
        
        when(mobileStyleConverter.convertToMobileHtml(anyString()))
                .thenReturn(testHtml1)
                .thenReturn(testHtml2);
        
        // 创建测试文件
        Path testFile1 = tempDir.resolve("batch-test1.html");
        Path testFile2 = tempDir.resolve("batch-test2.html");
        Files.write(testFile1, "test content 1".getBytes());
        Files.write(testFile2, "test content 2".getBytes());
        
        // 执行批量转换
        String[] filePaths = {testFile1.toString(), testFile2.toString()};
        CompletableFuture<MobileConversionResult[]> future = 
                conversionService.batchConvert(filePaths);
        
        // 等待结果
        MobileConversionResult[] results = future.get();
        
        // 验证结果
        assertNotNull(results);
        assertEquals(2, results.length);
        
        assertTrue(results[0].isSuccess());
        assertTrue(results[1].isSuccess());
    }
    
    @Test
    void testValidateConversionResult() {
        // 测试成功的转换结果
        String validHtml = "<!DOCTYPE html><html><head></head><body class=\"mobile-container\">" +
                "<div class=\"mobile-section\"></div></body></html>";
        MobileConversionResult successResult = MobileConversionResult.success(validHtml, null);
        
        assertTrue(conversionService.validateConversionResult(successResult));
        
        // 测试失败的转换结果
        MobileConversionResult failureResult = MobileConversionResult.failure("测试错误");
        assertFalse(conversionService.validateConversionResult(failureResult));
        
        // 测试无效HTML的转换结果
        MobileConversionResult invalidHtmlResult = MobileConversionResult.success("invalid html", null);
        assertFalse(conversionService.validateConversionResult(invalidHtmlResult));
        
        // 测试空HTML的转换结果
        MobileConversionResult emptyHtmlResult = MobileConversionResult.success("", null);
        assertFalse(conversionService.validateConversionResult(emptyHtmlResult));
        
        // 测试null HTML的转换结果
        MobileConversionResult nullHtmlResult = MobileConversionResult.success(null, null);
        assertFalse(conversionService.validateConversionResult(nullHtmlResult));
    }
    
    @Test
    void testConversionStats() throws IOException {
        // 准备测试数据
        String testHtml = "<html><body><div class=\"mobile-section\">测试</div>" +
                "<div class=\"mobile-section\">内容</div></body></html>";
        when(mobileStyleConverter.convertToMobileHtml(anyString())).thenReturn(testHtml);
        
        // 创建测试文件
        Path testFile = tempDir.resolve("stats-test.html");
        String originalContent = "original test content for stats";
        Files.write(testFile, originalContent.getBytes());
        
        // 执行转换
        MobileConversionResult result = conversionService.convertToMobile(testFile.toString());
        
        // 验证统计信息
        assertNotNull(result.getConversionStats());
        assertEquals(originalContent.getBytes().length, result.getConversionStats().getOriginalHtmlSize());
        assertEquals(testHtml.getBytes().length, result.getConversionStats().getMobileHtmlSize());
        assertTrue(result.getConversionStats().getConversionDurationMs() >= 0);
        assertEquals(2, result.getConversionStats().getConvertedSectionsCount()); // 2个mobile-section
    }
}
