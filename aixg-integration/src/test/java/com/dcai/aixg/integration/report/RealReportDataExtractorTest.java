package com.dcai.aixg.integration.report;

import com.dcai.aixg.integration.report.model.ReportData;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.io.IOException;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 使用真实HTML文件的报告数据提取器测试类
 */
class RealReportDataExtractorTest {
    
    private ReportDataExtractor extractor;
    
    @BeforeEach
    void setUp() {
        extractor = new ReportDataExtractor();
    }
    
    @Test
    void testExtractRealReportData() throws IOException {
        // 使用项目中的真实HTML文件
        String htmlFilePath = "../docs/需求/报告.html";
        
        // 执行数据提取
        ReportData reportData = extractor.extractReportData(htmlFilePath);
        
        // 验证基本信息
        assertNotNull(reportData);
        assertEquals("上海市瀚林世家1室价格测评报告", reportData.getTitle());
        assertEquals("2025-06-27", reportData.getGenerateDate());
        assertEquals("2025年06月", reportData.getEvaluationTime());
        assertEquals("中国房地产决策咨询系统（CRIC）、市场公开数据", reportData.getDataSource());
        assertNotNull(reportData.getDisclaimer());
        
        // 验证小区信息
        assertNotNull(reportData.getCommunityInfo());
        assertEquals("武定路1128弄(武定路地铁站1号口步行400米)", reportData.getCommunityInfo().getAddress());
        assertEquals("上海市", reportData.getCommunityInfo().getCity());
        assertEquals("静安区", reportData.getCommunityInfo().getDistrict());
        assertEquals("曹家渡", reportData.getCommunityInfo().getArea());
        
        // 验证测评结论
        assertNotNull(reportData.getEvaluationResult());
        assertEquals("1室", reportData.getEvaluationResult().getRoomType());
        assertEquals("106,876元/㎡", reportData.getEvaluationResult().getEvaluationPrice());
        
        // 验证整体评价
        assertNotNull(reportData.getEvaluationResult().getOverallEvaluations());
        assertEquals(5, reportData.getEvaluationResult().getOverallEvaluations().size());
        
        // 验证评测分析
        assertNotNull(reportData.getAnalysisData());
        assertNotNull(reportData.getAnalysisData().getCompetitorDescription());
        
        System.out.println("真实HTML文件数据提取成功！");
        System.out.println("报告标题: " + reportData.getTitle());
        System.out.println("小区地址: " + reportData.getCommunityInfo().getAddress());
        System.out.println("评测价格: " + reportData.getEvaluationResult().getEvaluationPrice());
        System.out.println("整体评价项数: " + reportData.getEvaluationResult().getOverallEvaluations().size());
    }
    
    @Test
    void testExtractRealReportDataAsJson() throws IOException {
        // 使用项目中的真实HTML文件
        String htmlFilePath = "../docs/需求/报告.html";
        
        // 执行JSON提取
        String jsonResult = extractor.extractReportDataAsJson(htmlFilePath);
        
        // 验证JSON结果
        assertNotNull(jsonResult);
        assertFalse(jsonResult.isEmpty());
        assertTrue(jsonResult.contains("上海市瀚林世家1室价格测评报告"));
        assertTrue(jsonResult.contains("106,876元/㎡"));
        
        System.out.println("=== 真实HTML文件提取的完整JSON数据 ===");
        System.out.println(jsonResult);
    }
}
