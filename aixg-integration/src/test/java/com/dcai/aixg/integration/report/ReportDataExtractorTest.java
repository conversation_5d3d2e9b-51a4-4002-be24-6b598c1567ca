package com.dcai.aixg.integration.report;

import com.dcai.aixg.integration.report.model.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 报告数据提取器测试类
 */
class ReportDataExtractorTest {
    
    private ReportDataExtractor extractor;
    
    @TempDir
    Path tempDir;
    
    @BeforeEach
    void setUp() {
        extractor = new ReportDataExtractor();
    }
    
    @Test
    void testExtractReportData() throws IOException {
        // 创建测试HTML文件
        String htmlContent = createTestHtmlContent();
        Path htmlFile = tempDir.resolve("test-report.html");
        Files.write(htmlFile, htmlContent.getBytes("UTF-8"));
        
        // 执行数据提取
        ReportData reportData = extractor.extractReportData(htmlFile.toString());
        
        // 验证基本信息
        assertNotNull(reportData);
        assertEquals("上海市瀚林世家1室价格测评报告", reportData.getTitle());
        assertEquals("2025-06-27", reportData.getGenerateDate());
        assertEquals("2025年06月", reportData.getEvaluationTime());
        assertEquals("中国房地产决策咨询系统（CRIC）、市场公开数据", reportData.getDataSource());
        assertNotNull(reportData.getDisclaimer());
        
        // 验证小区信息
        CommunityInfo communityInfo = reportData.getCommunityInfo();
        assertNotNull(communityInfo);
        assertEquals("武定路1128弄(武定路地铁站1号口步行400米)", communityInfo.getAddress());
        assertEquals("上海市", communityInfo.getCity());
        assertEquals("静安区", communityInfo.getDistrict());
        assertEquals("曹家渡", communityInfo.getArea());
        assertEquals("2005-2006", communityInfo.getConstructionYear());
        assertEquals("住宅", communityInfo.getPropertyType());
        assertEquals("735", communityInfo.getTotalHouseholds());
        assertEquals("35%", communityInfo.getGreenRate());
        assertEquals("400%", communityInfo.getFloorAreaRatio());
        assertEquals("板楼", communityInfo.getBuildingType());
        assertEquals("550", communityInfo.getParkingSpaces());
        assertEquals("上海静安置业(集团)有限公司", communityInfo.getDeveloper());
        assertEquals("巨星物业", communityInfo.getPropertyCompany());
        assertEquals("1.65-2.6元/月/㎡", communityInfo.getPropertyFee());
        
        // 验证测评结论
        EvaluationResult evaluationResult = reportData.getEvaluationResult();
        assertNotNull(evaluationResult);
        assertEquals("1室", evaluationResult.getRoomType());
        assertEquals("106,876元/㎡", evaluationResult.getEvaluationPrice());
        assertNotNull(evaluationResult.getPriceNote());
        
        // 验证整体评价
        assertNotNull(evaluationResult.getOverallEvaluations());
        assertEquals(5, evaluationResult.getOverallEvaluations().size());
        
        EvaluationResult.OverallEvaluation firstEvaluation = evaluationResult.getOverallEvaluations().get(0);
        assertEquals("1", firstEvaluation.getIndex());
        assertEquals("区域价值", firstEvaluation.getIndicator());
        assertNotNull(firstEvaluation.getEvaluation());
        
        // 验证评测分析
        AnalysisData analysisData = reportData.getAnalysisData();
        assertNotNull(analysisData);
        assertNotNull(analysisData.getCompetitorDescription());
        assertNotNull(analysisData.getCommunityEvaluationAnalysis());
        assertNotNull(analysisData.getRegionalValueAnalysis());
        assertNotNull(analysisData.getTransportationAnalysis());
        assertNotNull(analysisData.getLivingFacilitiesAnalysis());
        assertNotNull(analysisData.getEducationResourcesAnalysis());
        assertNotNull(analysisData.getCommunityQualityAnalysis());
    }
    
    @Test
    void testExtractReportDataAsJson() throws IOException {
        // 创建测试HTML文件
        String htmlContent = createTestHtmlContent();
        Path htmlFile = tempDir.resolve("test-report.html");
        Files.write(htmlFile, htmlContent.getBytes("UTF-8"));
        
        // 执行JSON提取
        String jsonResult = extractor.extractReportDataAsJson(htmlFile.toString());
        
        // 验证JSON结果
        assertNotNull(jsonResult);
        assertFalse(jsonResult.isEmpty());
        assertTrue(jsonResult.contains("上海市瀚林世家1室价格测评报告"));
        assertTrue(jsonResult.contains("2025-06-27"));
        assertTrue(jsonResult.contains("武定路1128弄"));
        assertTrue(jsonResult.contains("106,876元/㎡"));
        
        System.out.println("提取的JSON数据:");
        System.out.println(jsonResult);
    }
    
    @Test
    void testExtractReportDataWithNonExistentFile() {
        // 测试文件不存在的情况
        assertThrows(IOException.class, () -> {
            extractor.extractReportData("/non/existent/file.html");
        });
    }
    
    /**
     * 创建测试用的HTML内容（简化版）
     */
    private String createTestHtmlContent() {
        return """
            <!DOCTYPE html>
            <html lang="zh-CN">
            <head>
                <meta charset="UTF-8">
                <title>上海市瀚林世家1室价格测评报告</title>
            </head>
            <body>
                <div class="banner">
                    <h1>上海市瀚林世家1室价格测评报告</h1>
                    <p>生成日期：2025-06-27</p>
                </div>
                
                <div class="data-card">
                    <h3>评测时间</h3>
                    <p>2025年06月</p>
                </div>
                <div class="data-card">
                    <h3>数据来源</h3>
                    <p>中国房地产决策咨询系统（CRIC）、市场公开数据</p>
                </div>
                <div class="data-card">
                    <h3>免责申明</h3>
                    <p>评测是基于CRIC和市场公开数据，通过AI算法和模型运算得出结果，仅供参考</p>
                </div>
                
                <table>
                    <tr>
                        <td>地址</td>
                        <td>武定路1128弄(武定路地铁站1号口步行400米)</td>
                        <td>所属城市</td>
                        <td>上海市</td>
                    </tr>
                    <tr>
                        <td>所属行政区</td>
                        <td>静安区</td>
                        <td>所属板块</td>
                        <td>曹家渡</td>
                    </tr>
                    <tr>
                        <td>建筑年代</td>
                        <td>2005-2006</td>
                        <td>物业类型</td>
                        <td>住宅</td>
                    </tr>
                    <tr>
                        <td>总户数</td>
                        <td>735</td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td>绿化率</td>
                        <td>35%</td>
                        <td>容积率</td>
                        <td>400%</td>
                    </tr>
                    <tr>
                        <td>建筑类型</td>
                        <td>板楼</td>
                        <td>停车位</td>
                        <td>550</td>
                    </tr>
                    <tr>
                        <td>开发商</td>
                        <td>上海静安置业(集团)有限公司</td>
                        <td>物业公司</td>
                        <td>巨星物业</td>
                    </tr>
                    <tr>
                        <td>物业费</td>
                        <td>1.65-2.6元/月/㎡</td>
                        <td></td>
                        <td></td>
                    </tr>
                </table>
                
                <div class="bg-blue-50">
                    <p>户型：<span>1室</span></p>
                    <p>评测价格：<span>106,876元/㎡</span></p>
                    <p>（备注：以上评测价格是基于CRIC中国房地产决策咨询系统和市场公开数据，通过AI算法和模型运算得出结果，仅供参考。）</p>
                </div>
                
                <table>
                    <thead>
                        <tr>
                            <th>序号</th>
                            <th>指标</th>
                            <th>评价</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>1</td>
                            <td>区域价值</td>
                            <td>坐落于静安区武定路1128弄，处静安寺与曹家渡板块交界核心地带...</td>
                        </tr>
                        <tr>
                            <td>2</td>
                            <td>交通网络</td>
                            <td>交通便捷度优于静安区96%的小区...</td>
                        </tr>
                        <tr>
                            <td>3</td>
                            <td>生活配套</td>
                            <td>成熟社区配套极为齐全...</td>
                        </tr>
                        <tr>
                            <td>4</td>
                            <td>教育资源</td>
                            <td>教育配套评分超越静安区92%的小区...</td>
                        </tr>
                        <tr>
                            <td>5</td>
                            <td>小区品质</td>
                            <td>2004年建成，14栋楼共668户...</td>
                        </tr>
                    </tbody>
                </table>
                
                <h3>周边竞品</h3>
                <p>周边0.5公里有二手房项目139个，均价8.5万元/㎡，瀚林世家均价109054元/平方米，在周边整体房价中处于较高水平。</p>
                
                <div class="bg-gray-50">
                    <h3>瀚林世家小区评价分析</h3>
                    <p>截至2025年6月，瀚林世家的二手房均价约为104,000-116,000元/平方米...</p>
                    <h4>成交价格分析</h4>
                    <p>瀚林世家近期具体成交案例：</p>
                    <ul>
                        <li>2025年3月，成交单价约104,902元/平方米</li>
                        <li>2025年2月，成交单价约116,596元/平方米</li>
                    </ul>
                    <h4>与周边竞品对比</h4>
                    <p>瀚林世家与周边竞品相比具有明显的价格优势...</p>
                    <h4>价格走势</h4>
                    <p>瀚林世家的房价从2023年7月至今整体保持稳定...</p>
                    <p>价格差异主要受户型大小、楼层、朝向和装修状况等因素影响。</p>
                </div>
                
                <h3>区域价值</h3>
                <p>瀚林世家位于上海市静安区武定路1128弄...</p>
                
                <h3>交通网络</h3>
                <p>瀚林世家小区位于上海市静安区，拥有极为出色的交通网络配套...</p>
                
                <h3>生活配套</h3>
                <p>瀚林世家小区位于静安区武定路1128弄，作为2004年竣工的成熟住宅社区...</p>
                
                <h3>教育资源</h3>
                <p>瀚林世家小区位于上海市静安区，拥有极其丰富的教育资源配套...</p>
                
                <h3>小区品质</h3>
                <p>瀚林世家位于上海市静安区武定路1128弄，由上海静安置业（集团）有限公司开发...</p>
            </body>
            </html>
            """;
    }
}
