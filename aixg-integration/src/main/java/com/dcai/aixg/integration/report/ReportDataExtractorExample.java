package com.dcai.aixg.integration.report;

import com.dcai.aixg.integration.report.model.ReportData;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;

/**
 * 报告数据提取器使用示例
 */
@Slf4j
public class ReportDataExtractorExample {
    
    public static void main(String[] args) {
        ReportDataExtractor extractor = new ReportDataExtractor();
        
        try {
            // 示例1：提取报告数据对象
            String htmlFilePath = "../docs/需求/报告.html";
            ReportData reportData = extractor.extractReportData(htmlFilePath);
            
            System.out.println("=== 报告基本信息 ===");
            System.out.println("报告标题: " + reportData.getTitle());
            System.out.println("生成日期: " + reportData.getGenerateDate());
            System.out.println("评测时间: " + reportData.getEvaluationTime());
            System.out.println("数据来源: " + reportData.getDataSource());
            
            System.out.println("\n=== 小区信息 ===");
            System.out.println("地址: " + reportData.getCommunityInfo().getAddress());
            System.out.println("城市: " + reportData.getCommunityInfo().getCity());
            System.out.println("行政区: " + reportData.getCommunityInfo().getDistrict());
            System.out.println("板块: " + reportData.getCommunityInfo().getArea());
            System.out.println("建筑年代: " + reportData.getCommunityInfo().getConstructionYear());
            System.out.println("物业类型: " + reportData.getCommunityInfo().getPropertyType());
            System.out.println("总户数: " + reportData.getCommunityInfo().getTotalHouseholds());
            System.out.println("绿化率: " + reportData.getCommunityInfo().getGreenRate());
            System.out.println("容积率: " + reportData.getCommunityInfo().getFloorAreaRatio());
            System.out.println("建筑类型: " + reportData.getCommunityInfo().getBuildingType());
            System.out.println("停车位: " + reportData.getCommunityInfo().getParkingSpaces());
            System.out.println("开发商: " + reportData.getCommunityInfo().getDeveloper());
            System.out.println("物业公司: " + reportData.getCommunityInfo().getPropertyCompany());
            System.out.println("物业费: " + reportData.getCommunityInfo().getPropertyFee());
            
            System.out.println("\n=== 测评结论 ===");
            System.out.println("户型: " + reportData.getEvaluationResult().getRoomType());
            System.out.println("评测价格: " + reportData.getEvaluationResult().getEvaluationPrice());
            System.out.println("整体评价项数: " + reportData.getEvaluationResult().getOverallEvaluations().size());
            
            System.out.println("\n=== 整体评价详情 ===");
            reportData.getEvaluationResult().getOverallEvaluations().forEach(evaluation -> {
                System.out.println(evaluation.getIndex() + ". " + evaluation.getIndicator());
                System.out.println("   " + evaluation.getEvaluation().substring(0, Math.min(100, evaluation.getEvaluation().length())) + "...");
            });
            
            System.out.println("\n=== 评测分析 ===");
            System.out.println("周边竞品: " + reportData.getAnalysisData().getCompetitorDescription());
            System.out.println("区域价值分析长度: " + (reportData.getAnalysisData().getRegionalValueAnalysis() != null ? 
                reportData.getAnalysisData().getRegionalValueAnalysis().length() : 0) + " 字符");
            System.out.println("交通网络分析长度: " + (reportData.getAnalysisData().getTransportationAnalysis() != null ? 
                reportData.getAnalysisData().getTransportationAnalysis().length() : 0) + " 字符");
            System.out.println("生活配套分析长度: " + (reportData.getAnalysisData().getLivingFacilitiesAnalysis() != null ? 
                reportData.getAnalysisData().getLivingFacilitiesAnalysis().length() : 0) + " 字符");
            System.out.println("教育资源分析长度: " + (reportData.getAnalysisData().getEducationResourcesAnalysis() != null ? 
                reportData.getAnalysisData().getEducationResourcesAnalysis().length() : 0) + " 字符");
            System.out.println("小区品质分析长度: " + (reportData.getAnalysisData().getCommunityQualityAnalysis() != null ? 
                reportData.getAnalysisData().getCommunityQualityAnalysis().length() : 0) + " 字符");
            
            // 示例2：直接提取JSON字符串
            System.out.println("\n=== JSON格式数据 ===");
            String jsonData = extractor.extractReportDataAsJson(htmlFilePath);
            System.out.println("JSON数据长度: " + jsonData.length() + " 字符");
            System.out.println("JSON数据预览: " + jsonData.substring(0, Math.min(200, jsonData.length())) + "...");
            
        } catch (IOException e) {
            log.error("提取报告数据失败", e);
            System.err.println("提取报告数据失败: " + e.getMessage());
        }
    }
}
