package com.dcai.aixg.integration.report.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 小区信息模型
 */
@Data
public class CommunityInfo {
    
    /**
     * 地址
     */
    @JsonProperty("address")
    private String address;
    
    /**
     * 所属城市
     */
    @JsonProperty("city")
    private String city;
    
    /**
     * 所属行政区
     */
    @JsonProperty("district")
    private String district;
    
    /**
     * 所属板块
     */
    @JsonProperty("area")
    private String area;
    
    /**
     * 建筑年代
     */
    @JsonProperty("construction_year")
    private String constructionYear;
    
    /**
     * 物业类型
     */
    @JsonProperty("property_type")
    private String propertyType;
    
    /**
     * 总户数
     */
    @JsonProperty("total_households")
    private String totalHouseholds;
    
    /**
     * 绿化率
     */
    @JsonProperty("green_rate")
    private String greenRate;
    
    /**
     * 容积率
     */
    @JsonProperty("floor_area_ratio")
    private String floorAreaRatio;
    
    /**
     * 建筑类型
     */
    @JsonProperty("building_type")
    private String buildingType;
    
    /**
     * 停车位
     */
    @JsonProperty("parking_spaces")
    private String parkingSpaces;
    
    /**
     * 开发商
     */
    @JsonProperty("developer")
    private String developer;
    
    /**
     * 物业公司
     */
    @JsonProperty("property_company")
    private String propertyCompany;
    
    /**
     * 物业费
     */
    @JsonProperty("property_fee")
    private String propertyFee;
}
