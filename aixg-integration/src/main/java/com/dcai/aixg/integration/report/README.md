# 报告数据提取器

## 概述

报告数据提取器（ReportDataExtractor）是一个用于从HTML报告文件中提取业务数据并转换为JSON格式的工具。该工具专门用于解析房地产价格测评报告，提取其中的结构化数据。

## 功能特性

- **HTML解析**: 使用Jsoup库解析HTML文档
- **数据提取**: 提取报告中的基本信息、小区信息、测评结论和评测分析
- **JSON转换**: 将提取的数据转换为结构化的JSON格式
- **类型安全**: 使用强类型的数据模型确保数据完整性
- **异常处理**: 完善的异常处理机制

## 数据结构

### 主要数据模型

1. **ReportData** - 报告数据主模型
   - 基本信息：标题、生成日期、评测时间、数据来源、免责申明
   - 小区信息：CommunityInfo对象
   - 测评结论：EvaluationResult对象
   - 评测分析：AnalysisData对象

2. **CommunityInfo** - 小区信息模型
   - 地址、城市、行政区、板块
   - 建筑年代、物业类型、总户数
   - 绿化率、容积率、建筑类型
   - 停车位、开发商、物业公司、物业费

3. **EvaluationResult** - 测评结论模型
   - 户型、评测价格、价格备注
   - 整体评价列表（OverallEvaluation）

4. **AnalysisData** - 评测分析模型
   - 周边竞品描述
   - 小区评价分析详情
   - 区域价值、交通网络、生活配套、教育资源、小区品质分析

## 使用方法

### 基本用法

```java
// 创建提取器实例
ReportDataExtractor extractor = new ReportDataExtractor();

// 方法1：提取为数据对象
ReportData reportData = extractor.extractReportData("path/to/report.html");

// 方法2：直接提取为JSON字符串
String jsonData = extractor.extractReportDataAsJson("path/to/report.html");
```

### 详细示例

```java
@Component
public class ReportService {
    
    private final ReportDataExtractor extractor = new ReportDataExtractor();
    
    public ReportData processReport(String htmlFilePath) throws IOException {
        // 提取报告数据
        ReportData reportData = extractor.extractReportData(htmlFilePath);
        
        // 访问基本信息
        String title = reportData.getTitle();
        String generateDate = reportData.getGenerateDate();
        
        // 访问小区信息
        CommunityInfo communityInfo = reportData.getCommunityInfo();
        String address = communityInfo.getAddress();
        String city = communityInfo.getCity();
        
        // 访问测评结论
        EvaluationResult evaluationResult = reportData.getEvaluationResult();
        String roomType = evaluationResult.getRoomType();
        String price = evaluationResult.getEvaluationPrice();
        
        // 访问整体评价
        List<OverallEvaluation> evaluations = evaluationResult.getOverallEvaluations();
        for (OverallEvaluation evaluation : evaluations) {
            String indicator = evaluation.getIndicator();
            String content = evaluation.getEvaluation();
        }
        
        // 访问评测分析
        AnalysisData analysisData = reportData.getAnalysisData();
        String competitorDesc = analysisData.getCompetitorDescription();
        String regionalAnalysis = analysisData.getRegionalValueAnalysis();
        
        return reportData;
    }
    
    public String getReportAsJson(String htmlFilePath) throws IOException {
        return extractor.extractReportDataAsJson(htmlFilePath);
    }
}
```

## JSON输出格式

提取的JSON数据包含以下主要字段：

```json
{
  "title": "报告标题",
  "generate_date": "生成日期",
  "evaluation_time": "评测时间",
  "data_source": "数据来源",
  "disclaimer": "免责申明",
  "community_info": {
    "address": "地址",
    "city": "城市",
    "district": "行政区",
    "area": "板块",
    "construction_year": "建筑年代",
    "property_type": "物业类型",
    "total_households": "总户数",
    "green_rate": "绿化率",
    "floor_area_ratio": "容积率",
    "building_type": "建筑类型",
    "parking_spaces": "停车位",
    "developer": "开发商",
    "property_company": "物业公司",
    "property_fee": "物业费"
  },
  "evaluation_result": {
    "room_type": "户型",
    "evaluation_price": "评测价格",
    "price_note": "价格备注",
    "overall_evaluations": [
      {
        "index": "序号",
        "indicator": "指标",
        "evaluation": "评价内容"
      }
    ]
  },
  "analysis_data": {
    "competitor_description": "周边竞品描述",
    "community_evaluation_analysis": {
      "overview": "总体描述",
      "transaction_price_analysis": {
        "description": "描述",
        "transaction_cases": ["成交案例列表"]
      },
      "competitor_comparison": "竞品对比",
      "price_trend": "价格走势",
      "summary": "总结"
    },
    "regional_value_analysis": "区域价值分析",
    "transportation_analysis": "交通网络分析",
    "living_facilities_analysis": "生活配套分析",
    "education_resources_analysis": "教育资源分析",
    "community_quality_analysis": "小区品质分析"
  }
}
```

## 依赖

- **Jsoup 1.17.2**: HTML解析库
- **FastJSON**: JSON序列化（项目已有）
- **Jackson**: JSON注解支持（项目已有）
- **Lombok**: 简化代码（项目已有）

## 测试

项目包含完整的单元测试：

- `ReportDataExtractorTest`: 基础功能测试
- `RealReportDataExtractorTest`: 真实HTML文件测试

运行测试：
```bash
mvn test -Dtest=ReportDataExtractorTest
mvn test -Dtest=RealReportDataExtractorTest
```

## 注意事项

1. **文件路径**: 确保HTML文件路径正确且文件存在
2. **编码格式**: HTML文件应使用UTF-8编码
3. **HTML结构**: 提取器依赖特定的HTML结构，如果报告格式发生变化可能需要调整解析逻辑
4. **异常处理**: 使用时应妥善处理IOException异常

## 扩展性

如果需要支持新的报告格式或添加新的数据字段，可以：

1. 扩展数据模型类
2. 在ReportDataExtractor中添加相应的解析逻辑
3. 更新测试用例

## 性能考虑

- 对于大文件，建议使用流式处理
- 可以考虑添加缓存机制避免重复解析
- 对于批量处理，可以使用多线程提高效率
