package com.dcai.aixg.integration.report.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.util.List;

/**
 * 评测分析数据模型
 */
@Data
public class AnalysisData {
    
    /**
     * 周边竞品描述
     */
    @JsonProperty("competitor_description")
    private String competitorDescription;
    
    /**
     * 小区评价分析
     */
    @JsonProperty("community_evaluation_analysis")
    private CommunityEvaluationAnalysis communityEvaluationAnalysis;
    
    /**
     * 区域价值分析
     */
    @JsonProperty("regional_value_analysis")
    private String regionalValueAnalysis;
    
    /**
     * 交通网络分析
     */
    @JsonProperty("transportation_analysis")
    private String transportationAnalysis;
    
    /**
     * 生活配套分析
     */
    @JsonProperty("living_facilities_analysis")
    private String livingFacilitiesAnalysis;
    
    /**
     * 教育资源分析
     */
    @JsonProperty("education_resources_analysis")
    private String educationResourcesAnalysis;
    
    /**
     * 小区品质分析
     */
    @JsonProperty("community_quality_analysis")
    private String communityQualityAnalysis;
    
    /**
     * 小区评价分析详情
     */
    @Data
    public static class CommunityEvaluationAnalysis {
        
        /**
         * 总体描述
         */
        @JsonProperty("overview")
        private String overview;
        
        /**
         * 成交价格分析
         */
        @JsonProperty("transaction_price_analysis")
        private TransactionPriceAnalysis transactionPriceAnalysis;
        
        /**
         * 与周边竞品对比
         */
        @JsonProperty("competitor_comparison")
        private String competitorComparison;
        
        /**
         * 价格走势
         */
        @JsonProperty("price_trend")
        private String priceTrend;
        
        /**
         * 总结
         */
        @JsonProperty("summary")
        private String summary;
    }
    
    /**
     * 成交价格分析
     */
    @Data
    public static class TransactionPriceAnalysis {
        
        /**
         * 描述
         */
        @JsonProperty("description")
        private String description;
        
        /**
         * 成交案例列表
         */
        @JsonProperty("transaction_cases")
        private List<String> transactionCases;
    }
}
