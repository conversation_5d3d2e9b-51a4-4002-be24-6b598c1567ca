package com.dcai.aixg.integration.report;

import com.alibaba.fastjson.JSON;
import com.dcai.aixg.integration.report.model.*;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 报告数据提取器
 * 用于从HTML报告文件中提取业务数据并转换为JSON格式
 */
@Slf4j
@Component
public class ReportDataExtractor {
    
    /**
     * 从HTML文件提取报告数据
     * 
     * @param htmlFilePath HTML文件路径
     * @return 报告数据JSON字符串
     * @throws IOException 文件读取异常
     */
    public String extractReportDataAsJson(String htmlFilePath) throws IOException {
        ReportData reportData = extractReportData(htmlFilePath);
        return JSON.toJSONString(reportData, true);
    }
    
    /**
     * 从HTML文件提取报告数据
     * 
     * @param htmlFilePath HTML文件路径
     * @return 报告数据对象
     * @throws IOException 文件读取异常
     */
    public ReportData extractReportData(String htmlFilePath) throws IOException {
        log.info("开始提取HTML报告数据，文件路径: {}", htmlFilePath);
        
        // 解析HTML文档
        Document doc = Jsoup.parse(new File(htmlFilePath), "UTF-8");
        
        ReportData reportData = new ReportData();
        
        // 提取基本信息
        extractBasicInfo(doc, reportData);
        
        // 提取小区信息
        extractCommunityInfo(doc, reportData);
        
        // 提取测评结论
        extractEvaluationResult(doc, reportData);
        
        // 提取评测分析
        extractAnalysisData(doc, reportData);
        
        log.info("HTML报告数据提取完成");
        return reportData;
    }
    
    /**
     * 提取基本信息
     */
    private void extractBasicInfo(Document doc, ReportData reportData) {
        // 提取标题
        Element titleElement = doc.selectFirst(".banner h1");
        if (titleElement != null) {
            reportData.setTitle(titleElement.text().trim());
        }
        
        // 提取生成日期
        Element dateElement = doc.selectFirst(".banner p");
        if (dateElement != null) {
            String dateText = dateElement.text().trim();
            if (dateText.startsWith("生成日期：")) {
                reportData.setGenerateDate(dateText.substring(5));
            }
        }
        
        // 提取评测时间、数据来源、免责申明
        Elements dataCards = doc.select(".data-card");
        for (Element card : dataCards) {
            Element h3 = card.selectFirst("h3");
            Element p = card.selectFirst("p");
            if (h3 != null && p != null) {
                String title = h3.text().trim();
                String content = p.text().trim();
                
                switch (title) {
                    case "评测时间":
                        reportData.setEvaluationTime(content);
                        break;
                    case "数据来源":
                        reportData.setDataSource(content);
                        break;
                    case "免责申明":
                        reportData.setDisclaimer(content);
                        break;
                }
            }
        }
    }
    
    /**
     * 提取小区信息
     */
    private void extractCommunityInfo(Document doc, ReportData reportData) {
        CommunityInfo communityInfo = new CommunityInfo();
        
        // 查找小区信息表格
        Elements tables = doc.select("table");
        for (Element table : tables) {
            Elements rows = table.select("tr");
            for (Element row : rows) {
                Elements cells = row.select("td");
                if (cells.size() >= 4) {
                    // 处理每行的两组数据
                    extractCommunityInfoFromCells(cells.get(0), cells.get(1), communityInfo);
                    extractCommunityInfoFromCells(cells.get(2), cells.get(3), communityInfo);
                } else if (cells.size() >= 2) {
                    // 处理只有一组数据的行
                    extractCommunityInfoFromCells(cells.get(0), cells.get(1), communityInfo);
                }
            }
        }
        
        reportData.setCommunityInfo(communityInfo);
    }
    
    /**
     * 从表格单元格提取小区信息
     */
    private void extractCommunityInfoFromCells(Element keyCell, Element valueCell, CommunityInfo communityInfo) {
        if (keyCell == null || valueCell == null) {
            return;
        }
        
        String key = keyCell.text().trim();
        String value = valueCell.text().trim();
        
        if (key.isEmpty() || value.isEmpty()) {
            return;
        }
        
        switch (key) {
            case "地址":
                communityInfo.setAddress(value);
                break;
            case "所属城市":
                communityInfo.setCity(value);
                break;
            case "所属行政区":
                communityInfo.setDistrict(value);
                break;
            case "所属板块":
                communityInfo.setArea(value);
                break;
            case "建筑年代":
                communityInfo.setConstructionYear(value);
                break;
            case "物业类型":
                communityInfo.setPropertyType(value);
                break;
            case "总户数":
                communityInfo.setTotalHouseholds(value);
                break;
            case "绿化率":
                communityInfo.setGreenRate(value);
                break;
            case "容积率":
                communityInfo.setFloorAreaRatio(value);
                break;
            case "建筑类型":
                communityInfo.setBuildingType(value);
                break;
            case "停车位":
                communityInfo.setParkingSpaces(value);
                break;
            case "开发商":
                communityInfo.setDeveloper(value);
                break;
            case "物业公司":
                communityInfo.setPropertyCompany(value);
                break;
            case "物业费":
                communityInfo.setPropertyFee(value);
                break;
        }
    }

    /**
     * 提取测评结论
     */
    private void extractEvaluationResult(Document doc, ReportData reportData) {
        EvaluationResult evaluationResult = new EvaluationResult();

        // 提取户型和评测价格
        Element priceSection = doc.selectFirst(".bg-blue-50");
        if (priceSection != null) {
            Elements pElements = priceSection.select("p");
            for (Element p : pElements) {
                String text = p.text().trim();
                if (text.startsWith("户型：")) {
                    String roomType = p.selectFirst("span").text().trim();
                    evaluationResult.setRoomType(roomType);
                } else if (text.startsWith("评测价格：")) {
                    String price = p.selectFirst("span").text().trim();
                    evaluationResult.setEvaluationPrice(price);
                } else if (text.startsWith("（备注：")) {
                    evaluationResult.setPriceNote(text);
                }
            }
        }

        // 提取整体评价 - 查找包含评价数据的特定表格
        List<EvaluationResult.OverallEvaluation> overallEvaluations = new ArrayList<>();
        Elements tables = doc.select("table");
        for (Element table : tables) {
            // 查找包含"序号"、"指标"、"评价"表头的表格
            Elements headers = table.select("thead th");
            boolean isEvaluationTable = false;
            if (headers.size() >= 3) {
                String header1 = headers.get(0).text().trim();
                String header2 = headers.get(1).text().trim();
                String header3 = headers.get(2).text().trim();
                if ("序号".equals(header1) && "指标".equals(header2) && "评价".equals(header3)) {
                    isEvaluationTable = true;
                }
            }

            if (isEvaluationTable) {
                Elements evaluationRows = table.select("tbody tr");
                for (Element row : evaluationRows) {
                    Elements cells = row.select("td");
                    if (cells.size() >= 3) {
                        EvaluationResult.OverallEvaluation evaluation = new EvaluationResult.OverallEvaluation();
                        evaluation.setIndex(cells.get(0).text().trim());
                        evaluation.setIndicator(cells.get(1).text().trim());
                        evaluation.setEvaluation(cells.get(2).text().trim());
                        overallEvaluations.add(evaluation);
                    }
                }
                break; // 找到评价表格后退出循环
            }
        }
        evaluationResult.setOverallEvaluations(overallEvaluations);

        reportData.setEvaluationResult(evaluationResult);
    }

    /**
     * 提取评测分析
     */
    private void extractAnalysisData(Document doc, ReportData reportData) {
        AnalysisData analysisData = new AnalysisData();

        // 提取周边竞品描述
        Element competitorDesc = doc.selectFirst("h3:contains(周边竞品) + p");
        if (competitorDesc != null) {
            analysisData.setCompetitorDescription(competitorDesc.text().trim());
        }

        // 提取小区评价分析
        extractCommunityEvaluationAnalysis(doc, analysisData);

        // 提取各项分析内容
        extractDetailedAnalysis(doc, analysisData);

        reportData.setAnalysisData(analysisData);
    }

    /**
     * 提取小区评价分析
     */
    private void extractCommunityEvaluationAnalysis(Document doc, AnalysisData analysisData) {
        AnalysisData.CommunityEvaluationAnalysis communityAnalysis = new AnalysisData.CommunityEvaluationAnalysis();

        Element analysisSection = doc.selectFirst(".bg-gray-50");
        if (analysisSection != null) {
            // 提取总体描述
            Element firstP = analysisSection.selectFirst("p");
            if (firstP != null) {
                communityAnalysis.setOverview(firstP.text().trim());
            }

            // 提取成交价格分析
            extractTransactionPriceAnalysis(analysisSection, communityAnalysis);

            // 提取与周边竞品对比
            Element competitorSection = analysisSection.selectFirst("h4:contains(与周边竞品对比) + p");
            if (competitorSection != null) {
                communityAnalysis.setCompetitorComparison(competitorSection.text().trim());
            }

            // 提取价格走势
            Element trendSection = analysisSection.selectFirst("h4:contains(价格走势) + p");
            if (trendSection != null) {
                communityAnalysis.setPriceTrend(trendSection.text().trim());
            }

            // 提取总结
            Elements allPs = analysisSection.select("p");
            if (!allPs.isEmpty()) {
                Element lastP = allPs.last();
                if (lastP != null && !lastP.text().trim().isEmpty()) {
                    communityAnalysis.setSummary(lastP.text().trim());
                }
            }
        }

        analysisData.setCommunityEvaluationAnalysis(communityAnalysis);
    }

    /**
     * 提取成交价格分析
     */
    private void extractTransactionPriceAnalysis(Element analysisSection, AnalysisData.CommunityEvaluationAnalysis communityAnalysis) {
        AnalysisData.TransactionPriceAnalysis priceAnalysis = new AnalysisData.TransactionPriceAnalysis();

        Element priceDesc = analysisSection.selectFirst("h4:contains(成交价格分析) + p");
        if (priceDesc != null) {
            priceAnalysis.setDescription(priceDesc.text().trim());
        }

        // 提取成交案例
        List<String> transactionCases = new ArrayList<>();
        Elements listItems = analysisSection.select("ul li");
        for (Element li : listItems) {
            String caseText = li.text().trim();
            if (!caseText.isEmpty()) {
                transactionCases.add(caseText);
            }
        }
        priceAnalysis.setTransactionCases(transactionCases);

        communityAnalysis.setTransactionPriceAnalysis(priceAnalysis);
    }

    /**
     * 提取详细分析内容
     */
    private void extractDetailedAnalysis(Document doc, AnalysisData analysisData) {
        // 提取区域价值分析
        Element regionalValue = doc.selectFirst("h3:contains(区域价值)");
        if (regionalValue != null) {
            StringBuilder content = new StringBuilder();
            Element nextElement = regionalValue.nextElementSibling();
            while (nextElement != null && !nextElement.tagName().equals("h3")) {
                if (nextElement.tagName().equals("p")) {
                    content.append(nextElement.text().trim()).append("\n");
                }
                nextElement = nextElement.nextElementSibling();
            }
            analysisData.setRegionalValueAnalysis(content.toString().trim());
        }

        // 提取交通网络分析
        Element transportation = doc.selectFirst("h3:contains(交通网络)");
        if (transportation != null) {
            StringBuilder content = new StringBuilder();
            Element nextElement = transportation.nextElementSibling();
            while (nextElement != null && !nextElement.tagName().equals("h3")) {
                if (nextElement.tagName().equals("p")) {
                    content.append(nextElement.text().trim()).append("\n");
                }
                nextElement = nextElement.nextElementSibling();
            }
            analysisData.setTransportationAnalysis(content.toString().trim());
        }

        // 提取生活配套分析
        Element livingFacilities = doc.selectFirst("h3:contains(生活配套)");
        if (livingFacilities != null) {
            StringBuilder content = new StringBuilder();
            Element nextElement = livingFacilities.nextElementSibling();
            while (nextElement != null && !nextElement.tagName().equals("h3")) {
                if (nextElement.tagName().equals("p")) {
                    content.append(nextElement.text().trim()).append("\n");
                }
                nextElement = nextElement.nextElementSibling();
            }
            analysisData.setLivingFacilitiesAnalysis(content.toString().trim());
        }

        // 提取教育资源分析
        Element education = doc.selectFirst("h3:contains(教育资源)");
        if (education != null) {
            StringBuilder content = new StringBuilder();
            Element nextElement = education.nextElementSibling();
            while (nextElement != null && !nextElement.tagName().equals("h3")) {
                if (nextElement.tagName().equals("p")) {
                    content.append(nextElement.text().trim()).append("\n");
                }
                nextElement = nextElement.nextElementSibling();
            }
            analysisData.setEducationResourcesAnalysis(content.toString().trim());
        }

        // 提取小区品质分析
        Element communityQuality = doc.selectFirst("h3:contains(小区品质)");
        if (communityQuality != null) {
            StringBuilder content = new StringBuilder();
            Element nextElement = communityQuality.nextElementSibling();
            while (nextElement != null && !nextElement.tagName().equals("h3")) {
                if (nextElement.tagName().equals("p")) {
                    content.append(nextElement.text().trim()).append("\n");
                }
                nextElement = nextElement.nextElementSibling();
            }
            analysisData.setCommunityQualityAnalysis(content.toString().trim());
        }
    }
}
