package com.dcai.aixg.integration.report.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.util.List;

/**
 * 测评结论模型
 */
@Data
public class EvaluationResult {
    
    /**
     * 户型
     */
    @JsonProperty("room_type")
    private String roomType;
    
    /**
     * 评测价格
     */
    @JsonProperty("evaluation_price")
    private String evaluationPrice;
    
    /**
     * 价格备注
     */
    @JsonProperty("price_note")
    private String priceNote;
    
    /**
     * 整体评价列表
     */
    @JsonProperty("overall_evaluations")
    private List<OverallEvaluation> overallEvaluations;
    
    /**
     * 整体评价项
     */
    @Data
    public static class OverallEvaluation {
        
        /**
         * 序号
         */
        @JsonProperty("index")
        private String index;
        
        /**
         * 指标
         */
        @JsonProperty("indicator")
        private String indicator;
        
        /**
         * 评价内容
         */
        @JsonProperty("evaluation")
        private String evaluation;
    }
}
