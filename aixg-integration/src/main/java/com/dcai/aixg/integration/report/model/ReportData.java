package com.dcai.aixg.integration.report.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 报告数据模型
 */
@Data
public class ReportData {
    
    /**
     * 报告标题
     */
    @JsonProperty("title")
    private String title;
    
    /**
     * 生成日期
     */
    @JsonProperty("generate_date")
    private String generateDate;
    
    /**
     * 评测时间
     */
    @JsonProperty("evaluation_time")
    private String evaluationTime;
    
    /**
     * 数据来源
     */
    @JsonProperty("data_source")
    private String dataSource;
    
    /**
     * 免责申明
     */
    @JsonProperty("disclaimer")
    private String disclaimer;
    
    /**
     * 小区信息
     */
    @JsonProperty("community_info")
    private CommunityInfo communityInfo;
    
    /**
     * 测评结论
     */
    @JsonProperty("evaluation_result")
    private EvaluationResult evaluationResult;
    
    /**
     * 评测分析
     */
    @JsonProperty("analysis_data")
    private AnalysisData analysisData;
}
