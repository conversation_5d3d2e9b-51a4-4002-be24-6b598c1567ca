package com.dcai.aixg.integration.report.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 手机端转换结果模型
 */
@Data
public class MobileConversionResult {
    
    /**
     * 转换是否成功
     */
    @JsonProperty("success")
    private boolean success;
    
    /**
     * 转换后的HTML内容
     */
    @JsonProperty("mobile_html")
    private String mobileHtml;
    
    /**
     * 原始报告数据
     */
    @JsonProperty("report_data")
    private ReportData reportData;
    
    /**
     * 转换时间戳
     */
    @JsonProperty("conversion_timestamp")
    private long conversionTimestamp;
    
    /**
     * 错误信息（如果转换失败）
     */
    @JsonProperty("error_message")
    private String errorMessage;
    
    /**
     * 转换统计信息
     */
    @JsonProperty("conversion_stats")
    private ConversionStats conversionStats;
    
    /**
     * 转换统计信息
     */
    @Data
    public static class ConversionStats {
        
        /**
         * 原始HTML大小（字节）
         */
        @JsonProperty("original_html_size")
        private long originalHtmlSize;
        
        /**
         * 转换后HTML大小（字节）
         */
        @JsonProperty("mobile_html_size")
        private long mobileHtmlSize;
        
        /**
         * 转换耗时（毫秒）
         */
        @JsonProperty("conversion_duration_ms")
        private long conversionDurationMs;
        
        /**
         * 提取的数据项数量
         */
        @JsonProperty("extracted_data_count")
        private int extractedDataCount;
        
        /**
         * 转换的章节数量
         */
        @JsonProperty("converted_sections_count")
        private int convertedSectionsCount;
    }
    
    /**
     * 创建成功的转换结果
     */
    public static MobileConversionResult success(String mobileHtml, ReportData reportData) {
        MobileConversionResult result = new MobileConversionResult();
        result.setSuccess(true);
        result.setMobileHtml(mobileHtml);
        result.setReportData(reportData);
        result.setConversionTimestamp(System.currentTimeMillis());
        return result;
    }
    
    /**
     * 创建失败的转换结果
     */
    public static MobileConversionResult failure(String errorMessage) {
        MobileConversionResult result = new MobileConversionResult();
        result.setSuccess(false);
        result.setErrorMessage(errorMessage);
        result.setConversionTimestamp(System.currentTimeMillis());
        return result;
    }
}
