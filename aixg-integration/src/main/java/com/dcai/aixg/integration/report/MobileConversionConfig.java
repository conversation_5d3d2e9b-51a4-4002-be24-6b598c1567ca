package com.dcai.aixg.integration.report;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 手机端转换配置
 */
@Data
@Component
@ConfigurationProperties(prefix = "aixg.mobile.conversion")
public class MobileConversionConfig {
    
    /**
     * 是否启用手机端转换
     */
    private boolean enabled = true;
    
    /**
     * 最大文件大小（字节）
     */
    private long maxFileSize = 10 * 1024 * 1024; // 10MB
    
    /**
     * 转换超时时间（毫秒）
     */
    private long timeoutMs = 30000; // 30秒
    
    /**
     * 是否压缩HTML输出
     */
    private boolean compressHtml = true;
    
    /**
     * 是否包含原始数据
     */
    private boolean includeOriginalData = true;
    
    /**
     * 样式配置
     */
    private StyleConfig style = new StyleConfig();
    
    /**
     * 内容配置
     */
    private ContentConfig content = new ContentConfig();
    
    /**
     * 样式配置
     */
    @Data
    public static class StyleConfig {
        
        /**
         * 主题色
         */
        private String primaryColor = "#3182ce";
        
        /**
         * 次要色
         */
        private String secondaryColor = "#4299e1";
        
        /**
         * 背景色
         */
        private String backgroundColor = "#f5f7fa";
        
        /**
         * 文字颜色
         */
        private String textColor = "#333";
        
        /**
         * 基础字体大小
         */
        private String baseFontSize = "14px";
        
        /**
         * 标题字体大小
         */
        private String titleFontSize = "1.25rem";
        
        /**
         * 卡片圆角
         */
        private String cardBorderRadius = "0.5rem";
        
        /**
         * 卡片阴影
         */
        private String cardBoxShadow = "0 1px 3px rgba(0, 0, 0, 0.1)";
        
        /**
         * 间距单位
         */
        private String spacingUnit = "0.5rem";
    }
    
    /**
     * 内容配置
     */
    @Data
    public static class ContentConfig {
        
        /**
         * 是否显示基本信息
         */
        private boolean showBasicInfo = true;
        
        /**
         * 是否显示小区信息
         */
        private boolean showCommunityInfo = true;
        
        /**
         * 是否显示测评结论
         */
        private boolean showEvaluationResult = true;
        
        /**
         * 是否显示评测分析
         */
        private boolean showAnalysisData = true;
        
        /**
         * 是否显示图表
         */
        private boolean showCharts = false; // 手机端默认不显示复杂图表
        
        /**
         * 是否显示地图
         */
        private boolean showMaps = false; // 手机端默认不显示地图
        
        /**
         * 最大文本长度（超过则截断）
         */
        private int maxTextLength = 1000;
        
        /**
         * 是否启用文本截断
         */
        private boolean enableTextTruncation = true;
        
        /**
         * 截断后缀
         */
        private String truncationSuffix = "...";
        
        /**
         * 段落最大数量
         */
        private int maxParagraphs = 5;
    }
    
    /**
     * 获取样式变量映射
     */
    public String getStyleVariables() {
        return String.format(
            ":root { " +
            "--primary-color: %s; " +
            "--secondary-color: %s; " +
            "--background-color: %s; " +
            "--text-color: %s; " +
            "--base-font-size: %s; " +
            "--title-font-size: %s; " +
            "--card-border-radius: %s; " +
            "--card-box-shadow: %s; " +
            "--spacing-unit: %s; " +
            "}",
            style.getPrimaryColor(),
            style.getSecondaryColor(),
            style.getBackgroundColor(),
            style.getTextColor(),
            style.getBaseFontSize(),
            style.getTitleFontSize(),
            style.getCardBorderRadius(),
            style.getCardBoxShadow(),
            style.getSpacingUnit()
        );
    }
    
    /**
     * 检查文件大小是否超限
     */
    public boolean isFileSizeExceeded(long fileSize) {
        return fileSize > maxFileSize;
    }
    
    /**
     * 截断文本
     */
    public String truncateText(String text) {
        if (!content.isEnableTextTruncation() || text == null) {
            return text;
        }
        
        if (text.length() <= content.getMaxTextLength()) {
            return text;
        }
        
        return text.substring(0, content.getMaxTextLength()) + content.getTruncationSuffix();
    }
    
    /**
     * 限制段落数量
     */
    public String limitParagraphs(String text) {
        if (text == null || content.getMaxParagraphs() <= 0) {
            return text;
        }
        
        String[] paragraphs = text.split("\\n\\s*\\n");
        if (paragraphs.length <= content.getMaxParagraphs()) {
            return text;
        }
        
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < content.getMaxParagraphs(); i++) {
            if (i > 0) {
                result.append("\n\n");
            }
            result.append(paragraphs[i]);
        }
        
        if (content.isEnableTextTruncation()) {
            result.append("\n\n").append(content.getTruncationSuffix());
        }
        
        return result.toString();
    }
}
