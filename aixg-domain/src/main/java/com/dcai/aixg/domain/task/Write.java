package com.dcai.aixg.domain.task;

import static com.ejuetc.commons.base.spring.SpringUtil.getProperty;

import java.util.List;
import java.util.stream.Collectors;

import com.dcai.aixg.domain.broker.Broker;
import com.dcai.aixg.dto.task.WriteCreateDTO;
import com.dcai.aixg.pro.task.WriteCreatePO;
import com.ejuetc.commons.base.clazz.SubtypeCode;
import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.consumer.api.community.BindCommunityRO;
import com.ejuetc.consumer.api.community.CommunityAPI;
import com.ejuetc.saasapi.sdk.SaaSApiSDK;

import jakarta.persistence.Column;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

@Getter
@Entity
@Slf4j
@DiscriminatorValue("WRITE")
@SubtypeCode(parent = Task.class, code = "WRITE", name = "文章")
@Accessors(chain = true)
@NoArgsConstructor
public class Write extends Task {

    @Column(name = "write_id", columnDefinition = "varchar(255) COMMENT '写作ID'")
    private String writeId;

    @Column(name = "article_id", columnDefinition = "varchar(255) COMMENT '文章ID'")
    private String articleId;

    @Column(name = "write_type", columnDefinition = "varchar(16) COMMENT '文章类型; 1:房源推荐软文 2:购房指南 3:行业热点评论'")
    private String writeType;
    
    public Write(Broker broker, WriteCreateDTO dto, WriteCreatePO po) {
    	this.broker = broker;
    	this.writeType = po.getWriteType();
    	this.ask = po.getAddInfo();
    	this.writeId = dto.getWriteId();
    	this.articleId = dto.getArticleId();
    	this.topic = dto.getTopic();
    	this.title = dto.getTitle();
    }
    
    public Write(Broker broker, WriteCreatePO po) {
    	this.broker = broker;
    	this.writeType = po.getWriteType();
    	this.ask = po.getAddInfo();
    	if (po.getHouseInfo() != null && po.getHouseInfo().size() > 0) {
    		List<String> houseIds = po.getHouseInfo().stream().map(house -> house.getString("id")).collect(Collectors.toList());
    		this.houseIds = houseIds;
    		this.houseInfos = po.getHouseInfo().stream().map(house -> house.toString()).collect(Collectors.toList());
    		//this.houseInfos = po.getHouseInfo();
    	}
    }
    
    public void writeSubmit(WriteCreateDTO dto) {
    	this.writeId = dto.getWriteId();
    	this.articleId = dto.getArticleId();
    	this.topic = dto.getTopic();
    	this.title = dto.getTitle();
    }
    
    public String getEstateDistrict(String estateAddress, String estateName) {
        String saasKeyCode = getProperty("ejuetc.saasApi.keyCode");
        String saasKeySecret = getProperty("ejuetc.saasApi.keySecret");
        String saasHost = getProperty("ejuetc.saasApi.url");
        String consumerUrl = getProperty("ejuetc.consumer.url");
    	SaaSApiSDK sdk = new SaaSApiSDK(saasKeyCode, saasKeySecret, saasHost);
        CommunityAPI communityAPI = sdk.feignClient(CommunityAPI.class, consumerUrl);
        ApiResponse<BindCommunityRO> bind = communityAPI.bind(estateAddress, estateName);
        if (!bind.isSucc() || bind.getData() == null) return null;
        return bind.getData().getDistrict();
    }
    
}
